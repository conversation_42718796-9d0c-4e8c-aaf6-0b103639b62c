import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { AbstractControl, AsyncValidatorFn, FormBuilder, FormGroup, ValidationErrors, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';

import {
  PageEditStatusEnum,
  ContractModuleTypeEnum,
  ContractSubjectEventType,
  ContractTypeEnum,
  FactoryEnum,
} from '../../model/contract-management.enum';
import { ContractModel, ContractSubjectEvent, ElanContractRelatedDocRespOrderItem } from '../../model/contract-management.interface';
import { FlcValidatorService } from 'fl-common-lib';
import { format } from 'date-fns';
import { ContractManagementService } from '../../contract-management.service';
import { NzCascaderOption } from 'ng-zorro-antd/cascader';
import { Observable, timer, distinctUntilChanged, switchMap } from 'rxjs';
import { NzNotificationService } from 'ng-zorro-antd/notification';

/* 选择关联单据之后其他接口需要的数据 */
export interface SelectedRelatedDocsParams {
  factory_code: string[];
  related_ids: string[];
  supplier_id: string;
  contract_type: number[];
  isInit?: boolean;
}

const gapString = '###';

/* 关联单据选项 */
interface RelatedDocsOption extends ElanContractRelatedDocRespOrderItem {
  children: ElanContractRelatedDocRespOrderItem[];
  disable?: boolean;
}

@Component({
  selector: 'contract-basic-info',
  templateUrl: './contract-basic-info.component.html',
  styleUrls: ['./contract-basic-info.component.scss'],
})
export class ContractBasicInfoComponent implements OnInit, OnChanges {
  @Input() moduleType: ContractModuleTypeEnum = ContractModuleTypeEnum.procurementContract; // 当前是哪个模块在使用这个组件
  @Input() formConfig: any[] = [];
  @Input() editMode = PageEditStatusEnum.read;
  @Input() detailInfo: ContractModel = {};
  @Input() sign_location: NzCascaderOption[] = []; // 地址下拉
  @Input() sign_location_country: NzCascaderOption[] = []; // 地址下拉 国家省市区
  @Input() contract_template: { value: number; label: string }[] = []; // 模板下拉数据
  @Output() onTemplateChange = new EventEmitter();
  @Input() isContentChange = false;

  cacheData: {
    templateId?: number; //缓存合同模板变量
    company_id?: number; // 合同公司
    related_id?: number[]; // 缓存关联单据
    contract_type?: number[]; // 缓存合同类型
  } = {};

  translateName = '';
  basicInfoForm!: FormGroup;
  pageEditStatusEnum = PageEditStatusEnum;

  contractModuleTypeEnum = ContractModuleTypeEnum;

  contractTypeEnum = ContractTypeEnum;

  contract_type = [
    {
      label: '物料采购合同',
      value: ContractTypeEnum.materialPurchase,
    },
    {
      label: '成衣加工合同',
      value: ContractTypeEnum.garment,
    },
    {
      label: '二次工艺加工合同',
      value: ContractTypeEnum.secProcess,
    },
    {
      label: '打样合同',
      value: ContractTypeEnum.sample,
    },
  ];
  related_docs: RelatedDocsOption[] = [];

  _related_docs: RelatedDocsOption[] = []; // 关联单据原始下拉数据

  related_ids: Array<any> = [];

  company_options: any[] = [];


  // 工段、付款方式、结算方式数据
  payment_method: string[] = [];
  settlement_method: string[] = [];
  stages: string[] = [];

  constructor(
    private fb: FormBuilder,
    private flcValidatorService: FlcValidatorService,
    private contractManagementService: ContractManagementService,
    private _activeRoute: ActivatedRoute,
    private _notity: NzNotificationService
  ) { }

  isPartyFormChange = false; // 乙方是否更改
  isProcurementFormChange = false; //采购详情是否修改

  /// 是否是成衣加工合同
  get isGarmentOrder() {
    return this.basicInfoForm?.get('contract_type')?.value === ContractTypeEnum.garment;
  }

  ngOnInit() {
    // this.getPaymentId(); // 获取支付方式
    if (this.moduleType === ContractModuleTypeEnum.procurementContract) {
      this.contract_type.push({
        label: '成衣销售合同',
        value: ContractTypeEnum.sales,
      });
    }
    // 新建逻辑
    if (this.editMode === PageEditStatusEnum.add) {
      this.initForm();
      this.getCode(); //获取合同编号
      this.initSignAddress(); //回显本地签订地点缓存

      // 从销售合同跳转过来
      const type = this._activeRoute.snapshot.queryParams['type'];
      if (type && Number(type) === ContractTypeEnum.sales) {
        this.basicInfoForm?.get('contract_type')?.setValue(ContractTypeEnum.sales);
        this.initSalesType(ContractTypeEnum.sales);
      }
    }

    if ([ContractModuleTypeEnum.procurementContract, ContractModuleTypeEnum.procurementContractMesPro].includes(this.moduleType)) {
      this.contractManagementService.addSubjectListener(
        'basic-info',
        [ContractSubjectEventType.onFormChange],
        (res: ContractSubjectEvent) => {
          if (res.type === ContractSubjectEventType.onFormChange) {
            switch (res.data.type) {
              case 'contractPartyComponent':
                this.isPartyFormChange = res.data.isChange;
                break;
              case 'procurementDetailComponent':
                this.isProcurementFormChange = res.data.isChange;
            }
          }
        }
      );
    }

    // 专业版 合同类型只有物料采购
    if (this.moduleType === ContractModuleTypeEnum.procurementContractMesPro) {
      this.contract_type = [
        {
          label: '物料采购合同',
          value: ContractTypeEnum.materialPurchase,
        },
      ];
      if (this.editMode === PageEditStatusEnum.add) {
        this.basicInfoForm.get('contract_type')?.setValue(ContractTypeEnum.materialPurchase);
        this.selectChange(ContractTypeEnum.materialPurchase, { key: 'contract_type' }, true);
      }
    }
  }

  // 初始话成衣销售合同（从销售合同过来的）
  async initSalesType(val: any) {
    const related_id = this._activeRoute.snapshot.queryParams['id'];
    const contract_type = this.contract_type.filter((item) => val === item.value)[0];
    this.basicInfoForm.get('title')?.setValue(contract_type && contract_type.label);
    this.basicInfoForm.get('related_id')?.reset([], { emitViewToModelChange: false });
    this.contractManagementService.sendSubjectEvent(ContractSubjectEventType.onContractTypeChange, val);
    await this.getRelatedDocAsync(val);
    await this.getAsyncCustomerOptions();
    this.isPartyFormChange = false;
    this.isProcurementFormChange = false;
    this.cacheData.related_id = [];
    this.cacheData.contract_type = val;
    // 成衣销售合同关联单据单选
    this.basicInfoForm?.get('related_id')?.setValue(related_id ?? null);
    this.onRelatedIdChange(related_id ?? null, null, true);
  }

  ngOnDestroy() {
    this.contractManagementService.removeSubjectListener('basic-info');
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes?.detailInfo?.currentValue) {
      this.isPartyFormChange = false;
      this.isProcurementFormChange = false;
      this.initForm();
      this.cacheData.templateId = changes.detailInfo.currentValue.contract_template_id;
      this.cacheData.related_id = this.basicInfoForm.get('related_id')?.value;
      this.cacheData.contract_type = this.basicInfoForm.get('contract_type')?.value;

      // 如果是成衣加工合同，初始化工段等信息
      if (this.detailInfo?.contract_type === ContractTypeEnum.garment && this.detailInfo?.related_docs?.length && this.moduleType ==ContractModuleTypeEnum.procurementContractMes) {
        this.getRelatedDoc(this.detailInfo?.contract_type as number);
      }
      // 采购合同编辑时需要根据已有的合同类型获取关联单据下拉数据
      if ([ContractModuleTypeEnum.procurementContract, ContractModuleTypeEnum.procurementContractMesPro].includes(this.moduleType)) {
        this.getRelatedDoc(this.detailInfo?.contract_type as number);
      }
      if (this.moduleType === ContractModuleTypeEnum.procurementContract) {
        this.getCompanyOptions(changes.detailInfo.currentValue.contract_type);
      }
    }

    if (
      changes?.editMode?.currentValue &&
      changes?.editMode?.currentValue === PageEditStatusEnum.edit &&
      [ContractModuleTypeEnum.procurementContract, ContractModuleTypeEnum.procurementContractMesPro].includes(this.moduleType)
    ) {
      const val = this.detailInfo.related_docs?.map((item) => item.related_id);
      this.related_ids = val || [];
      this.getRelatedDoc(this.detailInfo?.contract_type as number);
      // if (this.basicInfoForm.get('contract_type')?.value !== ContractTypeEnum.sales) {
      //   this.selectChange(val, { key: 'related_id' }, true);
      // }
    }
  }

  /**支付方式下拉数据 */
  // getPaymentId() {
  //   return new Promise((resolve) => {
  //     this.contractManagementService.getDict({ column: 'settlement_method' }).subscribe((res: any) => {
  //       if (res.code === 200) {
  //         this.settlement_method = res.data.option_list || [];
  //         resolve(true);
  //       }
  //     });
  //   });
  // }

  initForm() {
    const group: any = {};
    this.formConfig.forEach((item) => {
      if (item.key === 'code') {
        group[item.key] = [this.detailInfo[item.key] || null, [Validators.required]];
      } else if (item.key === 'sign_location') {
        const _location = this.detailInfo[item.key] || {};
        const _value = this.getLocation(_location);
        group[item.key] = [_value, [Validators.required]];
      } else if (item.key === 'company_id') {
        if (
          this.detailInfo.contract_type &&
          [ContractTypeEnum.secProcess, ContractTypeEnum.sample, ContractTypeEnum.garment].includes(this.detailInfo.contract_type)
        ) {
          group[item.key] = [this.detailInfo.party_b_factory_code || null];
        } else {
          group[item.key] = [this.detailInfo.company_id];
        }
      } else if (item.key === 'related_id') {
        let ids = this.detailInfo[item.optionKey]?.map((item: any) => item.related_id);
        const codes = this.detailInfo[item.optionKey]?.map((item: any) => item.related_code);
        if (this.detailInfo.contract_type === ContractTypeEnum.garment) {
          ids = this.detailInfo[item.optionKey]?.map((e: any) => {
            return e.related_id + gapString + e.related_code;
          });
        }
        if (this.detailInfo?.contract_type === ContractTypeEnum.sales) {
          group[item.key] = [ids?.length ? ids[0] : null, [Validators.required]]; // 成衣销售合同，关联单据是单选
        } else {
          group[item.key] = [ids, [Validators.required]];
        }
        this.detailInfo[item.onlyReadKey] = codes?.join('、');
      } else if (item.key === 'contract_template_id') {
        // 适配合同模板被删掉时下拉宽依然展示数据
        this.detailInfo[item.optionKey] = [
          { value: this.detailInfo?.contract_template_id, label: this.detailInfo?.contract_template_name },
        ];
        group[item.key] = item.required ? [this.detailInfo[item.key] || null, [Validators.required]] : [this.detailInfo[item.key] || null];
      } else {
        group[item.key] = item.required ? [this.detailInfo[item.key] || null, [Validators.required]] : [this.detailInfo[item.key] || null];
      }
    });

    if (this.moduleType === ContractModuleTypeEnum.procurementContractMes) {
      group['contract_type'] = [this.detailInfo.contract_type || null, [Validators.required]];
    }
    this.basicInfoForm = this.fb.group(group);
    // 合同管理/框架协议需要检查编号唯一性，只有合同管理和框架协议需要唯一性校验
    switch (this.moduleType) {
      case this.contractModuleTypeEnum.frameworkAgreement:
      case this.contractModuleTypeEnum.procurementContract:
      case this.contractModuleTypeEnum.procurementContractMesPro:
        this.basicInfoForm.get('code')?.addAsyncValidators(this.validatorCode().bind(this));
        break;
      default:
        break;
    }
  }

  // 签订地点位置
  getLocation(_location: any) {
    if (!_location?.country_name) {
      return _location?.province_name ? [_location?.province_name, _location?.city_name, _location?.district_name] : null;
    } else {
      return _location?.province_name
        ? [_location?.country_name, _location?.province_name, _location?.city_name, _location?.district_name]
        : [_location?.country_name];
    }
  }

  async isFormValid(): Promise<boolean> {
    if (await this.flcValidatorService.formIsAsyncInvalid(this.basicInfoForm)) {
      this.flcValidatorService.markFormDirty(this.basicInfoForm);
      return false;
    }
    return true;
  }

  selectChange(val: any, item: any, isInit?: boolean) {
    if (!val && isInit) return;
    if ([ContractModuleTypeEnum.procurementContract, ContractModuleTypeEnum.procurementContractMesPro].includes(this.moduleType)) {
      // 当前模块为采购合同时
      switch (item.key) {
        // 合同模板
        case 'contract_template_id':
          // this.onTemplateChange.emit(val);
          this.onTemplateChangCondition(val);
          break;
        // 关联单据
        case 'related_id': {
          if (this.basicInfoForm?.get('contract_type')?.value === ContractTypeEnum.sales) {
            // 成衣销售合同类型， 切换或清空，则需要提示
            if (!this.cacheData.related_id?.length) {
              this.onRelatedIdChange(val, item, isInit);
            } else {
              this.contractManagementService
                .confirmDialog('确认更换关联单据?', {
                  iconClass: 'icon-yiwen',
                  iconColor: '#FF6C33',
                  showSubContent: true,
                  subContent: '更换后，甲方、采购详情版块数据会被清除哦～',
                })
                .subscribe((res) => {
                  if (res) {
                    this.onRelatedIdChange(val, item, isInit);
                  } else {
                    this.basicInfoForm.get('related_id')?.setValue(this.cacheData.related_id, { emitViewToModelChange: false });
                  }
                });
            }
          } else {
            // 关联单据切换，需判断乙方和采购详情是否编辑过，编辑则提示
            if (this.isPartyFormChange || this.isProcurementFormChange) {
              this.contractManagementService
                .confirmDialog('确认更换关联单据?', {
                  iconClass: 'icon-yiwen',
                  iconColor: '#FF6C33',
                  showSubContent: true,
                  subContent: '更换后，乙方、采购详情版块数据会被清除哦～',
                })
                .subscribe((res) => {
                  if (res) {
                    this.onRelatedIdChange(val, item, isInit);
                  } else {
                    this.basicInfoForm.get('related_id')?.setValue(this.cacheData.related_id, { emitViewToModelChange: false });
                  }
                });
            } else {
              this.onRelatedIdChange(val, item, isInit);
            }
          }
          break;
        }
        // 合同类型切换
        case 'contract_type': {
          const type = this.cacheData.contract_type as any;
          if (type === ContractTypeEnum.sales) {
            // 成衣销售合同类型， 切换或清空，则需要提示
            this.contractManagementService
              .confirmDialog('确认更换合同类型?', {
                iconClass: 'icon-yiwen',
                iconColor: '#FF6C33',
                showSubContent: true,
                subContent: '更换后，关联单据，合同信息，采购详情版块数据会被清除哦～',
              })
              .subscribe((res) => {
                if (res) {
                  this.changeSignAddress(val);
                  this.onContractTypeChange(val);
                  this.getCompanyOptions(val);
                  this.contractManagementService.auto_sort = false; // 切换合同类型、关联单据、合作公司等触发采购详情数据变更的，会重置auto_sort
                } else {
                  this.basicInfoForm.get('contract_type')?.setValue(this.cacheData.contract_type, { emitViewToModelChange: false });
                }
              });
          } else {
            // 关联单据切换，需判断乙方和采购详情是否编辑过，编辑则提示
            if (this.isPartyFormChange || this.isProcurementFormChange) {
              this.contractManagementService
                .confirmDialog('确认更换合同类型?', {
                  iconClass: 'icon-yiwen',
                  iconColor: '#FF6C33',
                  showSubContent: true,
                  subContent: '更换后，关联单据，乙方，采购详情版块数据会被清除哦～',
                })
                .subscribe((res) => {
                  if (res) {
                    this.changeSignAddress(val);
                    this.onContractTypeChange(val);
                    this.getCompanyOptions(val);
                    this.contractManagementService.auto_sort = false;
                  } else {
                    this.basicInfoForm.get('contract_type')?.setValue(this.cacheData.contract_type, { emitViewToModelChange: false });
                  }
                });
            } else {
              this.changeSignAddress(val);
              this.onContractTypeChange(val);
              this.getCompanyOptions(val);
              this.contractManagementService.auto_sort = false;
            }
          }

          break;
        }
        // 选择合作公司, 筛选出合作公司相关的关联单据
        case 'company_id':
          this.onChangeCompany(val);
          break;
      }
    } else {
      // this.onTemplateChange.emit(val);
      this.onTemplateChangCondition(val);
    }
  }

  onSelectOptenChange(isOpen: boolean, item: any) {
    if (item.key === 'related_id' && isOpen) {
      this.getRelatedDocByCompany(this.basicInfoForm.get('company_id')?.value);
    }
  }

  // 修改合作公司
  private onChangeCompany(val: number) {
    if (this.isPartyFormChange || this.isProcurementFormChange) {
      this.contractManagementService
        .confirmDialog('确认更换合同公司?', {
          iconClass: 'icon-yiwen',
          iconColor: '#FF6C33',
          showSubContent: true,
          subContent: '更换后，关联单据，乙方，采购详情版块数据会被清除哦～',
        })
        .subscribe((res) => {
          if (res) {
            const _option = this.company_options.find((item) => val === item.unique_value);
            // 清空 关联单据、乙方信息、采购详情
            this.contractManagementService.sendSubjectEvent(ContractSubjectEventType.onCooperateCompanyChange, _option);
            this.basicInfoForm
              .get('related_id')
              ?.reset(this.basicInfoForm.get('contract_type')?.value === ContractTypeEnum.sales ? null : []);
            this.isPartyFormChange = false;
            this.isProcurementFormChange = false;
            this.cacheData.company_id = val;
            this.contractManagementService.auto_sort = false;
          } else {
            this.basicInfoForm.get('company_id')?.setValue(this.cacheData.company_id, { emitViewToModelChange: false });
          }
        });
    } else {
      const _option = this.company_options.find((item) => val === item.unique_value);
      // 清空 关联单据、乙方信息、采购详情
      this.basicInfoForm.get('related_id')?.reset(this.basicInfoForm.get('contract_type')?.value === ContractTypeEnum.sales ? null : []);
      this.contractManagementService.sendSubjectEvent(ContractSubjectEventType.onCooperateCompanyChange, _option);
      this.isPartyFormChange = false;
      this.isProcurementFormChange = false;
      this.cacheData.company_id = val;
      this.contractManagementService.auto_sort = false;
    }
  }

  /* 设置合作公司，筛选出合作公司相关的关联单据 */
  private getRelatedDocByCompany(value?: number | string) {
    const _docs = JSON.parse(JSON.stringify(this._related_docs));
    const _filter_list = _docs.filter((item: any) => {
      return item.related_contract_id === 0 || this.related_ids.includes(item.related_id);
    });

    // 设置关联单据下拉数据仅限合作公司
    if (!value) {
      this.classifyRleatedDocs(this.basicInfoForm.get('contract_type')?.value, _filter_list);
      return;
    }
    let _options: any[] = [];
    switch (this.basicInfoForm.get('contract_type')?.value) {
      case ContractTypeEnum.materialPurchase:
        _options = _filter_list.filter((item: any) => item.supplier_id === value);
        this.classifyRleatedDocs(this.basicInfoForm.get('contract_type')?.value, _options);
        break;
      case ContractTypeEnum.garment:
      case ContractTypeEnum.secProcess:
      case ContractTypeEnum.sample:
        _options = _filter_list.filter((item: any) => item.factory_code.includes(value));
        this.classifyRleatedDocs(this.basicInfoForm.get('contract_type')?.value, _options);
        break;
      case ContractTypeEnum.sales:
        _options = _filter_list.filter((item: any) => item.customer_id === value);
        this.classifyRleatedDocs(this.basicInfoForm.get('contract_type')?.value, _options);
        break;
    }
    this.classifyRleatedDocs(this.basicInfoForm.get('contract_type')?.value, _options);
  }

  searchText = '';
  onSearchKey(key: string | null, item: any) {
    this.searchText = key || '';
  }

  onFilterOption() {
    return (value: string, option: any) => {
      if (!value) return true;
      if (option.nzLabel.includes(value)) {
        return true;
      }
      const stlyleCodes = option.template?.elementRef.nativeElement?.parentElement?.stlyleCodes || [];
      for (let i = 0; i < stlyleCodes.length; i++) {
        if (stlyleCodes[i].includes(value)) {
          return true;
        }
      }
      return false;
    };
  }

  // 由成衣销售合同切换成其他合同，需清空签订地点，从缓存中取签订地点并赋值
  changeSignAddress(val: number | ContractTypeEnum) {
    const type = this.cacheData.contract_type as any;
    if (type === ContractTypeEnum.sales) {
      this.basicInfoForm?.get('sign_location')?.reset();
      this.initSignAddress();
    } else {
      if (type !== ContractTypeEnum.sales && val === ContractTypeEnum.sales) {
        this.basicInfoForm?.get('sign_location')?.reset();
      }
    }
  }

  //合同类型切换
  private onContractTypeChange(val: any) {
    const contract_type = this.contract_type.filter((item) => val === item.value)[0];
    this.basicInfoForm.get('title')?.setValue(contract_type && contract_type.label);
    this.basicInfoForm.get('related_id')?.reset([], { emitViewToModelChange: false });
    this.basicInfoForm.get('company_id')?.reset(null, { emitViewToModelChange: false });
    this.contractManagementService.sendSubjectEvent(ContractSubjectEventType.onContractTypeChange, val);
    this.getRelatedDoc(val);
    this.isPartyFormChange = false;
    this.isProcurementFormChange = false;
    this.cacheData.related_id = [];
    this.cacheData.contract_type = val;
  }

  // 关联单据切换
  onRelatedIdChange(val: any, item: any, isInit?: boolean) {
    // 成衣销售合同类型，切换单据， 调用销售合同的详情接口，获取甲方信息和采购详情、签订地点（若有值，则不需要重新赋值）
    if (this.basicInfoForm?.get('contract_type')?.value === ContractTypeEnum.sales) {
      const options = this._related_docs?.filter((doc) => val === doc.related_id);
      // 设置合作公司
      options?.length && !isInit && this.setCompanyValue(options[0]);
      const related_id = this.basicInfoForm.get('related_id')?.value ? [this.basicInfoForm.get('related_id')?.value] : [];
      this.sortData(options, related_id);
      const params = this.getSelectedRelatedDocsParams(options);
      if (val) {
        this.contractManagementService.getSalesContractDetail(val).subscribe((res: any) => {
          if (res?.code === 200) {
            // 默认带入销售单的目的国且不随关联单据的切换而切换
            const _location = res?.data.destination_country_name
              ? [
                res?.data?.destination_country_name,
                res?.data?.destination_province_name,
                res?.data?.destination_city_name,
                res?.data?.destination_distinct_name,
              ]
              : [];

            if (!this.basicInfoForm?.get('sign_location')?.value || !this.basicInfoForm?.get('sign_location')?.value?.length) {
              this.basicInfoForm?.get('sign_location')?.setValue(_location);
            }
            params['salesDetail'] = {
              ...res?.data,
              area: _location,
            };
            this.contractManagementService.sendSubjectEvent(ContractSubjectEventType.onRelatedDocsChange, { isInit, ...params });
          }
        });
      } else {
        this.contractManagementService.sendSubjectEvent(ContractSubjectEventType.onRelatedDocsChange, { isInit, ...params });
      }
      this.isPartyFormChange = false;
      this.isProcurementFormChange = false;
      this.cacheData.related_id = val;
      this.contractManagementService.auto_sort = false;
    } else {
      console.log(this._related_docs,val, item)

      // 对于成衣加工合同，需要同时比较 related_id 和 related_code
      let options: any[] = [];
      if (this.basicInfoForm?.get('contract_type')?.value === ContractTypeEnum.garment) {
        // 成衣加工合同：val 格式为 "related_id + gapString + related_code"
        options = this._related_docs?.filter((doc) => {
          return val.some((selectedVal: string) => {
            const [selectedId, selectedCode] = selectedVal.split(gapString);
            return doc.related_id === selectedId && doc.related_code === selectedCode;
          });
        }) || [];
      } else {
        // 其他合同类型：只比较 related_id
        options = this._related_docs?.filter((doc) => val.includes(doc.related_id)) || [];
      }

      // 设置合作公司
      options?.length && !isInit && this.setCompanyValue(options[0]);

      // 处理成衣加工合同的特殊字段
      if (this.basicInfoForm?.get('contract_type')?.value === ContractTypeEnum.garment) {
        this.handleGarmentOrderFields(options);
      }

      this.sortData(options, this.basicInfoForm.get('related_id')?.value);
      const params = this.getSelectedRelatedDocsParams(options);
      this.setOptionDisable(val, params.factory_code);
      this.contractManagementService.sendSubjectEvent(ContractSubjectEventType.onRelatedDocsChange, { isInit, ...params });
      this.isPartyFormChange = false;
      this.isProcurementFormChange = false;
      this.cacheData.related_id = val;
      this.contractManagementService.auto_sort = false;
    }
  }

  // 设置合作公司
  private setCompanyValue(option: any) {
    const _contract_type = this.basicInfoForm.get('contract_type')?.value;
    if (_contract_type === ContractTypeEnum.materialPurchase) {
      const _option = this.company_options.find((item) => option.supplier_id === item.unique_value);
      this.basicInfoForm.get('company_id')?.setValue(_option?.unique_value, { emitViewToModelChange: false });
      this.contractManagementService.sendSubjectEvent(ContractSubjectEventType.onCooperateCompanyChange, _option);
    } else if (
      _contract_type === ContractTypeEnum.garment ||
      _contract_type === ContractTypeEnum.secProcess ||
      _contract_type === ContractTypeEnum.sample
    ) {
      if (option?.factory_code?.[0] === '') {
        this._notity.error('', '请在加工厂档案关联针聪明地址');
      }
      const _option = this.company_options.find((item) => option.factory_code.includes(item.unique_value));
      this.basicInfoForm.get('company_id')?.setValue(_option?.unique_value, { emitViewToModelChange: false });
      this.contractManagementService.sendSubjectEvent(ContractSubjectEventType.onCooperateCompanyChange, _option);
    } else if (_contract_type === ContractTypeEnum.sales) {
      const _option = this.company_options.find((item) => option.customer_id === item.unique_value);
      this.basicInfoForm.get('company_id')?.setValue(_option?.unique_value, { emitViewToModelChange: false });
      this.contractManagementService.sendSubjectEvent(ContractSubjectEventType.onCooperateCompanyChange, _option);
    }
  }

  // 改变合同模板需判断是否合同条款编辑过
  onTemplateChangCondition(val: number) {
    if (this.isContentChange) {
      this.contractManagementService
        .confirmDialog('确认更换合同模板?', {
          iconClass: 'icon-yiwen',
          iconColor: '#FF6C33',
          showSubContent: true,
          subContent: '更换后，合同条款明细版块数据会被清除哦～',
        })
        .subscribe((res) => {
          if (res) {
            this.cacheData.templateId = val;
            this.onTemplateChange.emit(val);
          } else {
            this.basicInfoForm.get('contract_template_id')?.setValue(this.cacheData.templateId, { emitViewToModelChange: false });
          }
        });
    } else {
      this.cacheData.templateId = val;
      this.onTemplateChange.emit(val);
    }
  }

  /* 组装其他接口需要的参数 */
  getSelectedRelatedDocsParams(options: RelatedDocsOption[]) {
    const params: SelectedRelatedDocsParams = {
      factory_code: this.getSameFactoryCode(options),
      related_ids: [],
      supplier_id: '',
      contract_type: this.basicInfoForm.get('contract_type')?.value,
    };
    for (const item of options) {
      if (this.isGarmentOrder) {
        params.related_ids.push(item.related_id!.split(gapString)[0]);
      } else {
        params.related_ids.push(item.related_id!);
      }
      params.supplier_id = item.supplier_id!;
    }
    return params;
  }
  /* 取所选大货单或者打样单的加工厂的交集 */
  getSameFactoryCode(options: RelatedDocsOption[]) {
    const codeMap = new Map();
    const codes = [];
    for (const items of options) {
      for (const code of items.factory_code!) {
        if (codeMap.get(code)) {
          codeMap.set(code, codeMap.get(code) + 1);
        } else {
          codeMap.set(code, 1);
        }
      }
    }
    for (const [key, value] of codeMap.entries()) {
      if (value === options.length) {
        codes.push(key);
      }
    }
    return codes;
  }

  /* 选择采购单时，设置其他供应商的采购单选项不可选 */
  setOptionDisable(val: any[], factory_codes: string[]) {
    const contract_type = this.basicInfoForm.get('contract_type')?.value;
    if (!val.length) {
      for (const item of this.related_docs) {
        if (contract_type === ContractTypeEnum.materialPurchase) {
          item.children?.forEach((child: any) => {
            child.disable = false;
          });
        } else {
          item.disable = false;
        }
      }
      return;
    }
    const option: RelatedDocsOption = this.related_docs.filter(
      (item) => item.children?.findIndex((child) => child.related_id === val[0]) !== -1
    )[0];
    for (const item of this.related_docs) {
      if (contract_type === ContractTypeEnum.materialPurchase) {
        if (!option) return;
        if (item.supplier_id !== option.supplier_id) {
          item.children?.forEach((child: any) => {
            child.disable = true;
          });
        }
        // 只能选相同采购类型的物料采购单
        const _child_item = option.children?.find((child: any) => child.related_id === val[0]);
        if (_child_item) {
          item.children?.forEach((child: any) => {
            child.disable = child.procurement_type !== _child_item.procurement_type;
          });
        }
      } else {
        // 合同类型选择非采购合同，选择关联单据之后，其他单据所有的factory_code都不在所有选中的关联单据factory_code的交集中，需要将其置灰不能选
        let haveCode = false;
        for (const code of item.factory_code!) {
          if (factory_codes.includes(code)) {
            haveCode = true;
            break;
          }
        }
        item.disable = !haveCode;
      }
    }
  }
  /* 点击合同类型获取关联单据 */
  getRelatedDoc(type: number) {
    let data: any = { contract_type: type }
    if (this.moduleType === ContractModuleTypeEnum.procurementContractMes) {
      data.from_factory_code = this.detailInfo?.party_a_factory_code
    }
    this.contractManagementService.getRelatedDoc(data).subscribe((res) => {
      if (res.code === 200) {
        this._related_docs = JSON.parse(JSON.stringify(res.data.list));
        if (this.detailInfo?.contract_type === ContractTypeEnum.garment && this.detailInfo?.related_docs?.length) {
          console.log(this.detailInfo.related_docs)
          const options = this._related_docs?.filter((doc) =>
            this.detailInfo?.related_docs?.some((rd) => rd.related_id === doc.related_id && rd.related_code === doc.related_code)
          );
          this.handleGarmentOrderFields(options);
        }
        if (this.isGarmentOrder) {
          this._related_docs = this._related_docs.map((e: any) => {
            return { ...e, related_id: e.related_id + gapString + e.related_code };
          });
        }
        this.classifyRleatedDocs(type, res.data.list);
      }
    });
  }

  private classifyRleatedDocs(type: ContractTypeEnum, _related_docs: any[]) {
    const options: RelatedDocsOption[] = [];
    if (type === ContractTypeEnum.materialPurchase) {
      // 合同类型为采购合同需要按照供应商分组
      const optionMap = new Map();
      _related_docs.forEach((item: any) => {
        item.disable = false;
        const _item = optionMap.get(item.supplier_id);
        if (_item) {
          _item.children.push(item);
        } else {
          optionMap.set(item.supplier_id, { supplier_id: item.supplier_id, supplier_name: item.supplier_name, children: [item] });
        }
      });
      for (const [key, val] of optionMap.entries()) {
        options.push(val);
      }
      this.related_docs = options;
    } else if (type === ContractTypeEnum.garment) {
      // 成衣加工合同 需要变更related_id: related_id + related_code
      this.related_docs = _related_docs.map((e: any) => {
        if (e.related_id?.includes(gapString)) return { ...e };
        return { ...e, related_id: e.related_id + gapString + e.related_code };
      });
    } else {
      this.related_docs = _related_docs;
    }
  }

  // 只针对转成衣销售合同过来的，待优化
  getRelatedDocAsync(type: number) {
    return new Promise((reslove) => {
      this.contractManagementService.getRelatedDoc({ contract_type: type }).subscribe((res) => {
        if (res.code === 200) {
          this._related_docs = JSON.parse(JSON.stringify(res.data.list));
          this.related_docs = res.data.list;
          reslove(true);
        }
      });
    });
  }

  /* 获取合同编号 */
  private getCode() {
    this.contractManagementService.getCode(this.moduleType).subscribe((res) => {
      this.basicInfoForm.get('code')?.setValue(res.data.code);
    });
  }

  /**
   * 校验图稿编码唯一性
   * @returns
   */
  private validatorCode(): AsyncValidatorFn {
    return (control: AbstractControl): Observable<ValidationErrors | null> => {
      return timer(500).pipe(
        distinctUntilChanged(),
        switchMap(() => {
          if (!control.value && control.value !== 0) {
            return Promise.resolve(null);
          } else {
            const val: any = {
              code: control.value,
            };
            if (this.detailInfo.id) val.id = this.detailInfo.id;
            return new Promise<ValidationErrors | null>((resolve) => {
              this.contractManagementService.checkCode(this.moduleType, val).subscribe((res) => {
                if (res.data.exist) {
                  resolve({ duplicated: true });
                } else {
                  resolve(null);
                }
              });
            });
          }
        })
      );
    };
  }

  /* 新建时 查看本地缓存是否有签订地点数据，有则默认带出来 */
  private initSignAddress() {
    const _val = this.contractManagementService.getCacheDataStrorage(this.moduleType);
    if (_val.signAddress && _val.signAddress.length && this.basicInfoForm?.get('contract_type')?.value !== ContractTypeEnum.sales) {
      this.basicInfoForm.get('sign_location')?.setValue(_val.signAddress);
    }
  }

  /* 提交的时候需要 保存签订地点 */
  saveSignAddress() {
    const _signAddress = this.basicInfoForm.get('sign_location')?.value;
    if (_signAddress && _signAddress.length && this.basicInfoForm?.get('contract_type')?.value !== ContractTypeEnum.sales) {
      this.contractManagementService.setCacheDataStorage(this.moduleType, {
        signAddress: _signAddress,
      });
    }
  }

  /* 按照某个数组顺序排序 */
  sortData(arr: any[], sortBy: any[]) {
    arr.sort((a, b) => sortBy?.indexOf(a.related_id) - sortBy?.indexOf(b.related_id));
  }

  getPayload() {
    const data = this.basicInfoForm.getRawValue();
    const relatedOption = this._related_docs.filter((item) => data.related_id?.includes(item.related_id));
    // let option: any = {};
    // 当前下拉数据的某个单子没有的时候应该将原本的数据给后台，让后台来验证单据的变化
    // if (relatedOption.length !== data.related_id?.length) {
    //   const ids = relatedOption.map((item) => item.related_id);
    //   option = this.detailInfo.related_docs?.filter((item) => !ids.includes(item.related_id))[0];
    //   option && relatedOption.push(option);
    // }

    const _company_option = this.company_options.find((item) => item.unique_value === data.company_id);
    if ([ContractTypeEnum.secProcess, ContractTypeEnum.sample, ContractTypeEnum.garment].includes(data.contract_type)) {
      data.company_id = _company_option?.id;
    }
    data.company_name = _company_option?.name;
    this.sortData(relatedOption, data.related_id);
    const contract_type = this.basicInfoForm.get('contract_type')?.value;
    data.sign_date = data.sign_date ? Number(format(data.sign_date, 'T')) : null;
    if (data?.contract_type === ContractTypeEnum.sales) {
      data.sign_location = {
        country_name: data.sign_location?.[0] ?? '',
        province_name: data.sign_location?.[1] ?? '',
        city_name: data.sign_location?.[2] ?? '',
        district_name: data.sign_location?.[3] ?? '',
      };
      data['related_id'] = data?.related_id ? [data?.related_id] : [];
    } else {
      data.sign_location = {
        province_name: data.sign_location?.[0] ?? '',
        city_name: data.sign_location?.[1] ?? '',
        district_name: data.sign_location?.[2] ?? '',
      };
    }

    data.related_docs = [];
    const relatedtypeMap = new Map([
      [ContractTypeEnum.materialPurchase, 2],
      [ContractTypeEnum.garment, 1],
      [ContractTypeEnum.secProcess, 1],
      [ContractTypeEnum.sample, 3],
    ]);
    if (this.isGarmentOrder) {
      data.related_id = data.related_id.map((e: any) => {
        return e.split(gapString)[0];
      });
      // 添加成衣加工合同特有字段
      data.stages = this.stages;
      data.payment_method = this.payment_method;
      data.settlement_method = this.settlement_method;
    }
    for (const relatedDoc of relatedOption) {
      data.related_docs.push({
        related_code: relatedDoc.related_code,
        related_id: this.isGarmentOrder ? relatedDoc.related_id?.split(gapString)[0] : relatedDoc.related_id,
        type: relatedtypeMap.get(contract_type) || null,
      });
    }
    return data;
  }

  getSalesPayload() {
    const data = this.basicInfoForm.getRawValue();
    data['related_id'] = data?.related_id ? [data?.related_id] : [];
    const _company_option = this.company_options.find((item) => item.unique_value === data.company_id);
    data.company_name = _company_option?.name;
    const relatedOption = this._related_docs.find((item) => data.related_id?.[0] == item.related_id);
    data.sign_date = data.sign_date ? Number(format(data.sign_date, 'T')) : null;
    data.sign_location = {
      country_name: data.sign_location?.[0] ?? '',
      province_name: data.sign_location?.[1] ?? '',
      city_name: data.sign_location?.[2] ?? '',
      district_name: data.sign_location?.[3] ?? '',
    };

    data.related_docs = [
      {
        related_code: relatedOption?.related_code,
        related_id: relatedOption?.related_id,
        type: null,
      },
    ];

    return data;
  }

  haveContractCode() {
    return !!this.basicInfoForm.get('code')?.value;
  }

  /**
   * 物料采购合同 -- 供应商档案;
   * 成衣和二次工艺合同 -- 加工厂下拉
   * 成衣销售合同 -- 客户档案
   * @param val 合同类型
   * @returns
   */
  private getCompanyOptions(val?: ContractTypeEnum) {
    if (!val) {
      this.company_options = [];
      return;
    }

    switch (val) {
      case ContractTypeEnum.materialPurchase:
        this.getFactoryOptions({ type: FactoryEnum.supplier });
        break;
      case ContractTypeEnum.garment:
      case ContractTypeEnum.secProcess:
      case ContractTypeEnum.sample:
        this.getFactoryOptions({ type: FactoryEnum.factory });
        break;
      case ContractTypeEnum.sales:
        this.getCustomerOptions();
        break;
    }
  }

  // 获取加工厂和供应商下拉
  private getFactoryOptions(queryParams?: { ids?: number[]; codes?: number[]; type: FactoryEnum }) {
    this.contractManagementService.getFactoryOptions({ ...queryParams }).subscribe((res) => {
      if (res.code == 200) {
        res.data = res.data.filter((item) => item.code);
        res.data.forEach((item) => {
          item.unique_value = item.type === FactoryEnum.factory ? item.code : item.id;
        });
        this.company_options = res.data;
      }
    });
  }

  private getCustomerOptions() {
    this.contractManagementService.getCustomerOptions().subscribe((res) => {
      if (res.code == 200) {
        this.company_options = res.data.customers?.map((item: any) => {
          return {
            unique_value: item.value,
            name: item.name,
            ...item,
          };
        });
      }
    });
  }

  async getAsyncCustomerOptions() {
    return new Promise((resolve) => {
      this.contractManagementService.getCustomerOptions().subscribe((res) => {
        if (res.code == 200) {
          this.company_options = res.data.customers?.map((item: any) => {
            return {
              unique_value: item.value,
              name: item.name,
              ...item,
            };
          });
        }
        resolve(true);
      });
    });
  }

  // 处理工段、付款方式、结算方式
  private handleGarmentOrderFields(options: any[]) {
    if (!options?.length) {
      this.stages = [];
      this.payment_method = [];
      this.settlement_method = [];
      return;
    }

    // 获取所有不重复的值
    const uniqueStages = new Set<string>();
    const uniquePayment_method = new Set<string>();
    const uniqueSettlement_method = new Set<string>();
console.log(options)
    options.forEach((option) => {
      if (option.stages?.length) {
        option.stages.forEach((stage: any) => {
          uniqueStages.add(stage.stage_name);
        });
      }
      if (option.payment_method) {
        uniquePayment_method.add(option.payment_method);
      }
      if (option.settlement_method) {
        uniqueSettlement_method.add(option.settlement_method);
      }
    });

    this.stages = Array.from(uniqueStages);
    this.payment_method = Array.from(uniquePayment_method);
    this.settlement_method = Array.from(uniqueSettlement_method);

    // 更新表单值
    this.basicInfoForm?.get('stages')?.setValue(this.stages.join('、'));
    this.basicInfoForm?.get('payment_method')?.setValue(this.payment_method.join('、'));
    this.basicInfoForm?.get('settlement_method')?.setValue(this.settlement_method.join('、'));
  }
}
